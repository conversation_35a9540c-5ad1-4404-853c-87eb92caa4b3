import pandas as pd
import psycopg2
from urllib.parse import urlparse
from datetime import datetime, timedelta
import logging
import os
import subprocess
import sys
from send_email import send_email

# Configuração do banco
SUPABASE_URL = 'postgresql://postgres:<EMAIL>:5432/postgres'

# Data da prova
EXAM_DATE = datetime(2025, 8, 31, 9, 0)  # 31/08/2025 às 9h

class UserSummaryGenerator:
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        
    def get_database_connection(self):
        """Conecta com o Supabase"""
        try:
            parsed_url = urlparse(SUPABASE_URL)
            conn = psycopg2.connect(
                dbname=parsed_url.path.lstrip('/'),
                user=parsed_url.username,
                password=parsed_url.password,
                host=parsed_url.hostname,
                port=parsed_url.port
            )
            self.logger.info("Conexão com Supabase estabelecida")
            return conn
        except Exception as e:
            self.logger.error(f"Erro ao conectar: {e}")
            raise
    
    def get_total_questions_in_database(self):
        """Busca o total de questões disponíveis no banco"""
        
        query = """
        SELECT COUNT(DISTINCT d."id_question") as total_questions
        FROM "question" d
        INNER JOIN "questiontype" e ON d."tp_question" = e."tp_question"
        WHERE e."ds_tpquestion" IN ('rapidinhas', 'sprint')
        """
        
        try:
            conn = self.get_database_connection()
            result = pd.read_sql_query(query, conn)
            conn.close()
            return int(result['total_questions'].iloc[0])
        except Exception as e:
            self.logger.error(f"Erro ao buscar total de questões: {e}")
            return 0

    def get_total_questions_by_category(self):
        """Busca o total de questões disponíveis por categoria"""
        
        query = """
        SELECT 
            f."ds_category",
            COUNT(DISTINCT d."id_question") as total_questions
        FROM "question" d
        INNER JOIN "questiontype" e ON d."tp_question" = e."tp_question"
        INNER JOIN "questioncategory" f ON d."id_category" = f."id_questioncategory"
        WHERE e."ds_tpquestion" IN ('rapidinhas', 'sprint')
        GROUP BY f."ds_category"
        """
        
        try:
            conn = self.get_database_connection()
            result = pd.read_sql_query(query, conn)
            conn.close()
            # Retorna um dicionário {categoria: total}
            return dict(zip(result['ds_category'], result['total_questions']))
        except Exception as e:
            self.logger.error(f"Erro ao buscar total de questões por categoria: {e}")
            return {}
    
    def get_user_total_questions_answered(self, user_id):
        """Busca quantas questões diferentes o usuário já respondeu (histórico completo)"""
        
        # Debug: verificar o tipo e valor do user_id
        print(f"Debug get_user_total_questions_answered: user_id = {user_id}, tipo = {type(user_id)}")
        
        # Converter numpy.int64 para int Python nativo
        if hasattr(user_id, 'item'):
            user_id = user_id.item()
        else:
            user_id = int(user_id)
        
        query = """
        SELECT COUNT(DISTINCT a."id_question") as questions_answered
        FROM "quizquestion" a
        INNER JOIN "quiz" b ON a."id_quiz" = b."id_quiz"
        INNER JOIN "user" c ON b."id_user" = c."id_user" AND c."st_active" = 1
        INNER JOIN "question" d ON a."id_question" = d."id_question"
        INNER JOIN "questiontype" e ON d."tp_question" = e."tp_question"
        WHERE a."dt_answer" IS NOT NULL
        AND c."id_user" = %s
        AND e."ds_tpquestion" IN ('rapidinhas', 'sprint')
        """
        
        try:
            conn = self.get_database_connection()
            result = pd.read_sql_query(query, conn, params=[user_id])
            conn.close()
            
            # Debug para verificar o resultado
            questions_answered = int(result['questions_answered'].iloc[0]) if not result.empty else 0
            print(f"Debug: Usuário {user_id} respondeu {questions_answered} questões no total")
            
            return questions_answered
        except Exception as e:
            print(f"Debug: Erro na query - {e}")
            self.logger.error(f"Erro ao buscar questões respondidas pelo usuário {user_id}: {e}")
            return 0
    
    def get_available_users(self, days=30):
        """Lista usuários disponíveis nos últimos X dias"""
        
        end_date = datetime.now()
        start_date = end_date - timedelta(days=days)
        
        query = """
        SELECT DISTINCT 
            c."id_user",
            c."ds_whatname",
            c."ds_whatinternalname",
            c."ds_mail",
            COUNT(a."id_quizquestion") as total_questoes,
            MAX(a."dt_answer") as ultima_atividade
        FROM "quizquestion" a
        INNER JOIN "quiz" b ON a."id_quiz" = b."id_quiz"
        INNER JOIN "user" c ON b."id_user" = c."id_user" AND c."st_active" = 1
        INNER JOIN "question" d ON a."id_question" = d."id_question"
        INNER JOIN "questiontype" e ON d."tp_question" = e."tp_question"
        WHERE a."dt_answer" IS NOT NULL
        AND e."ds_tpquestion" IN ('rapidinhas', 'sprint')
        GROUP BY c."id_user", c."ds_whatname", c."ds_whatinternalname", c."ds_mail"
        ORDER BY ultima_atividade DESC;
        """
        
        try:
            conn = self.get_database_connection()
            df = pd.read_sql_query(query, conn, params=[start_date.strftime('%Y-%m-%d')])
            conn.close()
            return df
        except Exception as e:
            self.logger.error(f"Erro ao buscar usuários: {e}")
            return pd.DataFrame()
    
    def get_user_data(self, user_id, days=7):
        """Busca dados de um usuário específico"""
        
        end_date = datetime.now()
        start_date = end_date - timedelta(days=days)
        
        query = """
        SELECT *
        FROM "quizquestion" a
        INNER JOIN "quiz" b ON a."id_quiz" = b."id_quiz"
        INNER JOIN "user" c ON b."id_user" = c."id_user" AND c."st_active" = 1
        INNER JOIN "question" d ON a."id_question" = d."id_question"
        INNER JOIN "questiontype" e ON d."tp_question" = e."tp_question"
        INNER JOIN "questioncategory" f ON d."id_category" = f."id_questioncategory"
        WHERE a."dt_answer" IS NOT NULL
        AND a."dt_answer" >= %s
        AND a."dt_answer" <= %s
        AND c."id_user" = %s
        AND e."ds_tpquestion" IN ('rapidinhas', 'sprint')
        ORDER BY a."dt_answer"
        """
        
        try:
            conn = self.get_database_connection()
            df = pd.read_sql_query(query, conn, params=[
                start_date.strftime('%Y-%m-%d'),
                end_date.strftime('%Y-%m-%d'),
                user_id
            ])
            conn.close()
            return df
        except Exception as e:
            self.logger.error(f"Erro ao buscar dados do usuário: {e}")
            return pd.DataFrame()
    
    def analyze_user_performance(self, user_data, days=7):
        """Analisa o desempenho do usuário"""
        
        if user_data.empty:
            return None
        
        # Informações básicas
        user_info = user_data.iloc[0]
        # Garantir que user_id seja um valor escalar
        user_id_value = user_info['id_user']
        if hasattr(user_id_value, 'iloc'):
            user_id_value = user_id_value.iloc[0]
        elif hasattr(user_id_value, 'values'):
            user_id_value = user_id_value.values[0]
        
        user_name = user_info['ds_whatname'] or user_info['ds_whatinternalname'] or f"Usuário {user_id_value}"
        
        # Calcular dias até a prova
        days_to_exam = (EXAM_DATE - datetime.now()).days
        
        # Métricas gerais
        total_questions = len(user_data)
        correct_answers = len(user_data[user_data['ds_answer'] == user_data['ds_correct']])
        incorrect_answers = total_questions - correct_answers
        accuracy_rate = (correct_answers / total_questions) * 100 if total_questions > 0 else 0
        
        # Métricas separadas por tipo (rapidinhas e sprint)
        rapidinhas_data = user_data[user_data['ds_tpquestion'] == 'rapidinhas']
        sprint_data = user_data[user_data['ds_tpquestion'] == 'sprint']
        
        rapidinhas_total = len(rapidinhas_data)
        rapidinhas_correct = len(rapidinhas_data[rapidinhas_data['ds_answer'] == rapidinhas_data['ds_correct']])
        rapidinhas_accuracy = (rapidinhas_correct / rapidinhas_total) * 100 if rapidinhas_total > 0 else 0
        
        sprint_total = len(sprint_data)
        sprint_correct = len(sprint_data[sprint_data['ds_answer'] == sprint_data['ds_correct']])
        sprint_accuracy = (sprint_correct / sprint_total) * 100 if sprint_total > 0 else 0
        
        # Performance por categoria
        categories_performance = {}
        for category in user_data['ds_category'].unique():
            cat_data = user_data[user_data['ds_category'] == category]
            cat_correct = len(cat_data[cat_data['ds_answer'] == cat_data['ds_correct']])
            cat_total = len(cat_data)
            # Pegar o tp_category (maiores ou menores)
            tp_category = cat_data['tp_category'].iloc[0] if 'tp_category' in cat_data.columns else 'não informado'
            
            # Ajustar classificação: apenas Miscelânea é baixa, resto "menores" vira moderada
            if tp_category == 'menores' and category.lower() != 'miscelânea':
                tp_category = 'moderada'
            elif category.lower() == 'miscelânea':
                tp_category = 'baixa'
            elif tp_category == 'maiores':
                tp_category = 'alta'
                
            categories_performance[category] = {
                'total': cat_total,
                'correct': cat_correct,
                'accuracy': (cat_correct / cat_total) * 100 if cat_total > 0 else 0,
                'tp_category': tp_category
            }
        
        # Performance por dificuldade
        difficulty_performance = {}
        for difficulty in user_data['nr_difficulty'].unique():
            if pd.notna(difficulty):
                diff_data = user_data[user_data['nr_difficulty'] == difficulty]
                diff_correct = len(diff_data[diff_data['ds_answer'] == diff_data['ds_correct']])
                diff_total = len(diff_data)
                difficulty_performance[int(difficulty)] = {
                    'total': diff_total,
                    'correct': diff_correct,
                    'accuracy': (diff_correct / diff_total) * 100 if diff_total > 0 else 0
                }
        
        # Atividade por dia
        user_data['dt_answer'] = pd.to_datetime(user_data['dt_answer'])
        activity_by_day = {}
        
        # Análise de dias com/sem atividade
        unique_days = user_data['dt_answer'].dt.date.nunique()
        period_days = (user_data['dt_answer'].dt.date.max() - user_data['dt_answer'].dt.date.min()).days + 1
        days_without_activity = period_days - unique_days
        
        # Criar dicionário com todos os dias da semana
        start_date = user_data['dt_answer'].dt.date.min()
        end_date = user_data['dt_answer'].dt.date.max()
        
        # Mapear dias da semana para abreviações em português
        dias_semana = {
            0: 'Seg', 1: 'Ter', 2: 'Qua', 3: 'Qui', 
            4: 'Sex', 5: 'Sáb', 6: 'Dom'
        }
        
        # Preencher todos os dias do período
        current_date = start_date
        while current_date <= end_date:
            day_data = user_data[user_data['dt_answer'].dt.date == current_date]
            dia_semana_abrev = dias_semana[current_date.weekday()]
            data_key = f"{current_date.strftime('%d/%m')}\n{dia_semana_abrev}"
            
            if len(day_data) > 0:
                day_correct = len(day_data[day_data['ds_answer'] == day_data['ds_correct']])
                activity_by_day[data_key] = {
                    'questions': len(day_data),
                    'correct': day_correct,
                    'accuracy': (day_correct / len(day_data)) * 100
                }
            else:
                # Dia sem atividade
                activity_by_day[data_key] = {
                    'questions': 0,
                    'correct': 0,
                    'accuracy': 0
                }
            
            current_date += timedelta(days=1)
        
        # Buscar total de questões respondidas pelo usuário (histórico completo)
        user_total_answered = self.get_user_total_questions_answered(user_id_value)
        total_questions_db = self.get_total_questions_in_database()
        
        # Buscar total de questões por categoria
        total_by_category = self.get_total_questions_by_category()
        
        # Adicionar total de questões no banco para cada categoria
        for category in categories_performance:
            categories_performance[category]['total_in_bank'] = total_by_category.get(category, 0)
        
        # Se o total de questões respondidas for 0, mas temos questões no período,
        # vamos contar as questões únicas do período como fallback
        if user_total_answered == 0 and total_questions > 0:
            unique_questions_in_period = user_data['id_question'].nunique()
            # Garantir que é um número
            if hasattr(unique_questions_in_period, 'item'):
                unique_questions_in_period = unique_questions_in_period.item()
            self.logger.warning(f"Total histórico retornou 0, usando questões únicas do período: {unique_questions_in_period}")
            user_total_answered = unique_questions_in_period
        
        return {
            'user_id': user_id_value,  # Agora é um valor escalar
            'name': user_name,
            'total_questions': total_questions,
            'correct_answers': correct_answers,
            'incorrect_answers': incorrect_answers,
            'accuracy_rate': accuracy_rate,
            'rapidinhas': {
                'total': rapidinhas_total,
                'correct': rapidinhas_correct,
                'accuracy': rapidinhas_accuracy
            },
            'sprint': {
                'total': sprint_total,
                'correct': sprint_correct,
                'accuracy': sprint_accuracy
            },
            'categories_performance': categories_performance,
            'difficulty_performance': difficulty_performance,
            'activity_by_day': activity_by_day,
            'period': f"{(datetime.now() - timedelta(days=days)).strftime('%d/%m/%Y')} a {datetime.now().strftime('%d/%m/%Y')}",
            'unique_days_studied': unique_days,
            'period_days': period_days,
            'days_without_activity': days_without_activity,
            'user_total_answered': int(user_total_answered) if hasattr(user_total_answered, 'item') else user_total_answered,
            'total_questions_db': total_questions_db,
            'days_to_exam': days_to_exam
        }
    
    def generate_suggestions(self, performance):
        """Gera exatamente 4 sugestões personalizadas focadas na prova"""
        
        suggestions = []
        
        # Sugestão 1: Progresso no banco de questões
        if performance['user_total_answered'] > 0:
            progress_percentage = (performance['user_total_answered'] / performance['total_questions_db']) * 100
            suggestions.append(
                f"📊 Você já respondeu {performance['user_total_answered']} de {performance['total_questions_db']} "
                f"questões disponíveis ({progress_percentage:.1f}% do banco). "
                f"Continue explorando para cobrir mais conteúdo!"
            )
        else:
            suggestions.append(
                f"📊 O banco contém {performance['total_questions_db']} questões disponíveis. "
                f"Continue praticando para explorar todo o conteúdo disponível!"
            )
        
        # Sugestão 2: Atividade no período atual
        suggestions.append(
            f"📅 No período analisado: {performance['correct_answers']} acertos de {performance['total_questions']} "
            f"questões respondidas ({performance['accuracy_rate']:.1f}% de aproveitamento). "
            f"Foque em manter a consistência!"
        )
        
        # Sugestão 3: Frequência de estudo
        if performance['days_without_activity'] > 0:
            suggestions.append(
                f"⏰ Você estudou {performance['unique_days_studied']} dias e ficou {performance['days_without_activity']} "
                f"dias sem atividade no período. Para a prova do dia 31/08, é essencial estudar TODOS OS DIAS! "
                f"A consistência é a chave do sucesso."
            )
        else:
            suggestions.append(
                f"🎯 Parabéns! Você estudou todos os {performance['unique_days_studied']} dias do período analisado. "
                f"Continue com essa disciplina até a prova do dia 31/08!"
            )
        
        # Sugestão 4: Contagem regressiva para a prova + foco em categorias de alta incidência
        alta_incidencia_baixa = []
        for cat, data in performance['categories_performance'].items():
            if data.get('tp_category') == 'alta' and data['accuracy'] < 70:
                alta_incidencia_baixa.append(cat)
        
        if performance['days_to_exam'] > 0:
            sugestao_prova = f"🎓 FALTAM APENAS {performance['days_to_exam']} DIAS para a prova (31/08/2025 às 9h)! "
            if alta_incidencia_baixa:
                sugestao_prova += f"ATENÇÃO ESPECIAL: As categorias de ALTA INCIDÊNCIA '{', '.join(alta_incidencia_baixa[:2])}' precisam de mais estudo!"
            else:
                sugestao_prova += "Continue focando nas categorias de ALTA INCIDÊNCIA para garantir o melhor resultado!"
            suggestions.append(sugestao_prova)
        else:
            suggestions.append(
                f"🎓 A prova é HOJE (31/08/2025 às 9h)! Confie no seu preparo, mantenha a calma e "
                f"dê o seu melhor. Boa sorte!"
            )
        
        return suggestions
    
    def print_summary(self, performance):
        """Imprime o resumo formatado no console"""
        
        print("\n" + "="*80)
        print(f"           RESUMO DE DESEMPENHO - {performance['name'].upper()}")
        print("="*80)
        
        print(f"\n📅 PERÍODO: {performance['period']}")
        print(f"👤 USUÁRIO: {performance['name']} (ID: {performance['user_id']})")
        print(f"🎓 DIAS PARA A PROVA: {performance['days_to_exam']} dias (31/08/2025 às 9h)")
        
        print(f"\n📊 MÉTRICAS GERAIS:")
        print(f"   • Total de questões respondidas: {performance['total_questions']}")
        print(f"   • Questões corretas: {performance['correct_answers']}")
        print(f"   • Questões incorretas: {performance['incorrect_answers']}")
        print(f"   • Taxa de aproveitamento: {performance['accuracy_rate']:.1f}%")
        
        print(f"\n🎯 MÉTRICAS POR TIPO DE QUESTÃO:")
        print(f"   📝 RAPIDINHAS: {performance['rapidinhas']['correct']}/{performance['rapidinhas']['total']} " +
              f"({performance['rapidinhas']['accuracy']:.1f}%)")
        print(f"   🏃 SPRINT / SIMULADOS: {performance['sprint']['correct']}/{performance['sprint']['total']} " +
              f"({performance['sprint']['accuracy']:.1f}%)")
        
        print(f"\n📈 PROGRESSO NO BANCO DE QUESTÕES:")
        if performance['user_total_answered'] > 0:
            progress_pct = (performance['user_total_answered'] / performance['total_questions_db']) * 100
            print(f"   • Questões já respondidas (histórico): {performance['user_total_answered']}")
            print(f"   • Total de questões no banco: {performance['total_questions_db']}")
            print(f"   • Progresso geral: {progress_pct:.1f}%")
        else:
            print(f"   • Total de questões no banco: {performance['total_questions_db']}")
            print(f"   • Não foi possível recuperar o histórico completo de questões")
            print(f"   • Continue praticando para aumentar seu progresso!")
        
        print(f"\n📅 FREQUÊNCIA DE ESTUDO:")
        print(f"   • Dias com atividade: {performance['unique_days_studied']}")
        print(f"   • Dias sem atividade: {performance['days_without_activity']}")
        print(f"   • Período analisado: {performance['period_days']} dias")
        
        # Atividade por dia
        print(f"\n📅 ATIVIDADE DIÁRIA:")
        for day, data in performance['activity_by_day'].items():
            # Extrair apenas a data sem o dia da semana para o console
            day_display = day.split('\n')[0] if '\n' in day else day
            if data['questions'] > 0:
                print(f"   • {day_display}: {data['correct']}/{data['questions']} questões ({data['accuracy']:.1f}%)")
            else:
                print(f"   • {day_display}: Sem atividade")
        
        # Performance por categoria
        print(f"\n🎯 PERFORMANCE POR CATEGORIA:")
        sorted_categories = sorted(performance['categories_performance'].items(), 
                                 key=lambda x: x[1]['accuracy'], reverse=True)
        for category, data in sorted_categories:
            status = "🟢" if data['accuracy'] >= 80 else "🟡" if data['accuracy'] >= 60 else "🔴"
            incidencia = "🔴" if data.get('tp_category') == 'alta' else "🟡" if data.get('tp_category') == 'moderada' else "⚪" if data.get('tp_category') == 'baixa' else "❓"
            incidencia_texto = f" [{data.get('tp_category', 'não informado').upper()} INCIDÊNCIA]" if data.get('tp_category') else ""
            
            # Calcular progresso na categoria
            total_in_bank = data.get('total_in_bank', 0)
            if total_in_bank > 0:
                category_progress = (data['correct'] / total_in_bank) * 100
                progress_text = f" | Progresso: {data['correct']}/{total_in_bank} ({category_progress:.1f}%)"
            else:
                progress_text = ""
            
            print(f"   {status} {incidencia} {category}{incidencia_texto}: {data['correct']}/{data['total']} ({data['accuracy']:.1f}%){progress_text}")
        
        # Performance por dificuldade
        if performance['difficulty_performance']:
            print(f"\n⭐ PERFORMANCE POR DIFICULDADE:")
            for difficulty in sorted(performance['difficulty_performance'].keys()):
                data = performance['difficulty_performance'][difficulty]
                stars = "⭐" * difficulty
                print(f"   {stars} Nível {difficulty}: {data['correct']}/{data['total']} ({data['accuracy']:.1f}%)")
        
        # Sugestões (sempre 4)
        suggestions = self.generate_suggestions(performance)
        print(f"\n💡 SUGESTÕES PERSONALIZADAS PARA A PROVA:")
        for i, suggestion in enumerate(suggestions, 1):
            print(f"   {i}. {suggestion}")
        
        print("\n" + "="*80)
    
    def save_summary_to_file(self, performance, filename=None):
        """Salva o resumo em um arquivo HTML"""
        
        if not filename:
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            user_id = performance['user_id']
            # Garantir que user_id é um valor simples
            if hasattr(user_id, 'iloc'):
                user_id = user_id.iloc[0]
            elif hasattr(user_id, 'values'):
                user_id = user_id.values[0]
            filename = f"resumo_usuario_{user_id}_{timestamp}.html"
        
        # Gera HTML
        html_content = self.generate_html_summary(performance)
        
        try:
            with open(filename, 'w', encoding='utf-8') as f:
                f.write(html_content)
            print(f"\n💾 Resumo salvo em: {filename}")
            return filename
        except Exception as e:
            print(f"❌ Erro ao salvar arquivo: {e}")
            # Tenta com nome mais simples
            try:
                simple_filename = f"resumo_{timestamp}.html"
                with open(simple_filename, 'w', encoding='utf-8') as f:
                    f.write(html_content)
                print(f"💾 Resumo salvo com nome alternativo: {simple_filename}")
                return simple_filename
            except Exception as e2:
                print(f"❌ Erro final ao salvar: {e2}")
                return None
    
    def save_summary_to_pdf(self, html_filename, pdf_filename=None):
        """Converte o HTML em PDF usando wkhtmltopdf ou weasyprint"""
        
        if not pdf_filename:
            pdf_filename = html_filename.replace('.html', '.pdf')
        
        try:
            # Primeiro tenta com weasyprint (mais fácil de instalar com pip)
            try:
                import weasyprint
                print(f"\n🔄 Convertendo para PDF usando WeasyPrint...")
                weasyprint.HTML(filename=html_filename).write_pdf(pdf_filename)
                print(f"✅ PDF salvo em: {pdf_filename}")
                return pdf_filename
            except ImportError:
                pass
            
            # Se não tem weasyprint, tenta wkhtmltopdf
            if sys.platform == 'win32':
                wkhtmltopdf_paths = [
                    r'C:\Program Files\wkhtmltopdf\bin\wkhtmltopdf.exe',
                    r'C:\Program Files (x86)\wkhtmltopdf\bin\wkhtmltopdf.exe',
                    'wkhtmltopdf.exe'
                ]
            else:
                wkhtmltopdf_paths = ['/usr/bin/wkhtmltopdf', '/usr/local/bin/wkhtmltopdf', 'wkhtmltopdf']
            
            wkhtmltopdf_path = None
            for path in wkhtmltopdf_paths:
                if os.path.exists(path) or os.system(f'which {path} > /dev/null 2>&1') == 0:
                    wkhtmltopdf_path = path
                    break
            
            if wkhtmltopdf_path:
                print(f"\n🔄 Convertendo para PDF usando wkhtmltopdf...")
                cmd = [wkhtmltopdf_path, '--enable-local-file-access', html_filename, pdf_filename]
                subprocess.run(cmd, check=True, capture_output=True)
                print(f"✅ PDF salvo em: {pdf_filename}")
                return pdf_filename
            else:
                print(f"\n⚠️ Para gerar PDF automaticamente, instale uma das opções:")
                print("   1. pip install weasyprint")
                print("   2. Baixe wkhtmltopdf de: https://wkhtmltopdf.org/downloads.html")
                return None
                
        except Exception as e:
            print(f"❌ Erro ao gerar PDF: {e}")
            print(f"⚠️ O HTML foi salvo com sucesso. Você pode:")
            print("   1. Abrir o HTML no navegador e usar Ctrl+P para imprimir como PDF")
            print("   2. Instalar weasyprint: pip install weasyprint")
            return None
        """Salva o resumo em um arquivo HTML"""
        
        if not filename:
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            user_id = performance['user_id']
            # Garantir que user_id é um valor simples
            if hasattr(user_id, 'iloc'):
                user_id = user_id.iloc[0]
            elif hasattr(user_id, 'values'):
                user_id = user_id.values[0]
            filename = f"resumo_usuario_{user_id}_{timestamp}.html"
        
        # Gera HTML
        html_content = self.generate_html_summary(performance)
        
        try:
            with open(filename, 'w', encoding='utf-8') as f:
                f.write(html_content)
            print(f"\n💾 Resumo salvo em: {filename}")
            return filename
        except Exception as e:
            print(f"❌ Erro ao salvar arquivo: {e}")
            # Tenta com nome mais simples
            try:
                simple_filename = f"resumo_{timestamp}.html"
                with open(simple_filename, 'w', encoding='utf-8') as f:
                    f.write(html_content)
                print(f"💾 Resumo salvo com nome alternativo: {simple_filename}")
                return simple_filename
            except Exception as e2:
                print(f"❌ Erro final ao salvar: {e2}")
                return None
    
    def generate_html_summary(self, performance):
        """Gera versão HTML do resumo"""
        
        # Atividade por dia (gráfico simples)
        activity_bars = ""
        for day, data in performance['activity_by_day'].items():
            if data['questions'] > 0:
                bar_height = min(data['questions'] * 3, 40)
                color = "#4CAF50" if data['accuracy'] >= 80 else "#FF9800" if data['accuracy'] >= 60 else "#F44336"
            else:
                bar_height = 2  # Barra mínima para dias sem atividade
                color = "#e0e0e0"
            
            # Separar data e dia da semana
            day_parts = day.split('\n')
            date_part = day_parts[0] if len(day_parts) > 0 else day
            weekday_part = day_parts[1] if len(day_parts) > 1 else ""
            
            activity_bars += f"""
            <div style="display: inline-block; margin: 0 3px; text-align: center; vertical-align: bottom;">
                <div style="width: 25px; height: {bar_height}px; background: {color}; 
                           margin-bottom: 1px; border-radius: 2px; border: 1px solid #ddd;"></div>
                <div style="font-size: 7px;">{date_part}</div>
                <div style="font-size: 7px; font-weight: bold;">{weekday_part}</div>
                <div style="font-size: 6px;">{data['questions'] if data['questions'] > 0 else '-'}<br>quest.</div>
            </div>
            """
        
        # Categorias
        categories_html = ""
        sorted_categories = sorted(performance['categories_performance'].items(), 
                                 key=lambda x: x[1]['accuracy'], reverse=True)
        for category, data in sorted_categories:
            color = "#4CAF50" if data['accuracy'] >= 80 else "#FF9800" if data['accuracy'] >= 60 else "#F44336"
            # Badge de incidência
            incidencia_badge = ""
            if data.get('tp_category') == 'alta':
                incidencia_badge = '<span style="background: #FF6B6B; color: white; padding: 2px 6px; border-radius: 3px; font-size: 10px; margin-left: 5px;">ALTA INCIDÊNCIA</span>'
            elif data.get('tp_category') == 'moderada':
                incidencia_badge = '<span style="background: #FFA500; color: white; padding: 2px 6px; border-radius: 3px; font-size: 10px; margin-left: 5px;">INCIDÊNCIA MODERADA</span>'
            elif data.get('tp_category') == 'baixa':
                incidencia_badge = '<span style="background: #4ECDC4; color: white; padding: 2px 6px; border-radius: 3px; font-size: 10px; margin-left: 5px;">BAIXA INCIDÊNCIA</span>'
            
            # Calcular progresso na categoria
            total_in_bank = data.get('total_in_bank', 0)
            progress_html = ""
            if total_in_bank > 0:
                category_progress = (data['correct'] / total_in_bank) * 100
                progress_html = f'<br><small style="font-size: 10px; color: #666;">Progresso no banco: {data["correct"]}/{total_in_bank} questões ({category_progress:.1f}%)</small>'
            
            # Badge de incidência
            incidencia_badge = ""
            if data.get('tp_category') == 'alta':
                incidencia_badge = '<span style="background: #FF6B6B; color: white; padding: 1px 4px; border-radius: 2px; font-size: 8px; margin-left: 3px;">ALTA</span>'
            elif data.get('tp_category') == 'moderada':
                incidencia_badge = '<span style="background: #FFA500; color: white; padding: 1px 4px; border-radius: 2px; font-size: 8px; margin-left: 3px;">MODERADA</span>'
            elif data.get('tp_category') == 'baixa':
                incidencia_badge = '<span style="background: #4ECDC4; color: white; padding: 1px 4px; border-radius: 2px; font-size: 8px; margin-left: 3px;">BAIXA</span>'
            
            # Calcular progresso na categoria
            total_in_bank = data.get('total_in_bank', 0)
            progress_html = ""
            if total_in_bank > 0:
                category_progress = (data['correct'] / total_in_bank) * 100
                progress_html = f'<br><small style="font-size: 9px; color: #666;">Progresso: {data["correct"]}/{total_in_bank} ({category_progress:.1f}%)</small>'
            
            categories_html += f"""
            <div style="margin: 4px 0; padding: 6px; border-left: 2px solid {color}; 
                       background: #f8f9fa; border-radius: 2px;">
                <strong style="font-size: 10px;">{category}</strong>{incidencia_badge}<br>
                <small style="font-size: 9px;">{data['correct']}/{data['total']} tent. • {data['accuracy']:.1f}% acertos</small>
                {progress_html}
            </div>
            """
        
        # Sugestões
        suggestions = self.generate_suggestions(performance)
        suggestions_html = ""
        for suggestion in suggestions:
            suggestions_html += f"<li style='margin: 3px 0; font-size: 10px; line-height: 1.3;'>{suggestion}</li>"
        
        # Barra de progresso geral
        progress_percentage = (performance['user_total_answered'] / performance['total_questions_db']) * 100 if performance['user_total_answered'] > 0 else 0
        
        html_template = f"""
        <!DOCTYPE html>
        <html>
        <head>
            <meta charset="UTF-8">
            <title>Resumo - {performance['name']}</title>
            <style>
                @page {{
                    size: A4;
                    margin: 10mm;
                }}
                body {{ 
                    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; 
                    line-height: 1.4; 
                    color: #333; 
                    margin: 0; 
                    padding: 0; 
                    background: white;
                    font-size: 14px;
                }}
                .container {{ 
                    max-width: 100%; 
                    margin: 0 auto; 
                    background: white; 
                    border-radius: 10px; 
                    overflow: hidden; 
                    box-shadow: 0 2px 10px rgba(0,0,0,0.1); 
                }}
                .header {{ 
                    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); 
                    color: white; 
                    padding: 20px; 
                    text-align: center; 
                }}
                .header h1 {{ 
                    margin: 0; 
                    font-size: 26px; 
                    font-weight: 300; 
                }}
                .header p {{ 
                    margin: 8px 0 0 0; 
                    opacity: 0.9; 
                    font-size: 13px;
                }}
                .exam-countdown {{
                    background: #FFD700; 
                    color: #333; 
                    padding: 12px; 
                    text-align: center; 
                    font-weight: bold; 
                    font-size: 16px;
                }}
                .content {{ 
                    padding: 20px; 
                }}
                .metric-row {{ 
                    display: grid; 
                    grid-template-columns: repeat(4, 1fr); 
                    gap: 15px; 
                    margin: 20px 0; 
                }}
                .metric-card {{ 
                    background: #f8f9fa; 
                    padding: 15px; 
                    border-radius: 8px; 
                    text-align: center; 
                    border: 1px solid #e9ecef; 
                }}
                .metric-number {{ 
                    font-size: 24px; 
                    font-weight: bold; 
                    margin-bottom: 3px; 
                }}
                .metric-label {{ 
                    color: #666; 
                    font-size: 12px; 
                }}
                .section {{ 
                    margin: 20px 0; 
                }}
                .section h2 {{ 
                    color: #667eea; 
                    margin-bottom: 15px; 
                    font-size: 20px; 
                }}
                .section h3 {{ 
                    font-size: 16px; 
                    margin-bottom: 10px; 
                }}
                .progress-bar {{ 
                    background: #e0e0e0; 
                    border-radius: 15px; 
                    height: 10px; 
                    margin: 10px 0; 
                    overflow: hidden; 
                }}
                .progress-fill {{ 
                    height: 100%; 
                    border-radius: 15px; 
                    background: linear-gradient(to right, #4CAF50, #81C784); 
                }}
                .type-metrics {{
                    display: grid; 
                    grid-template-columns: 1fr 1fr; 
                    gap: 15px; 
                    margin: 15px 0;
                }}
                .type-card {{
                    background: #f8f9fa; 
                    padding: 15px; 
                    border-radius: 8px; 
                    text-align: center;
                }}
                .type-card h3 {{
                    margin: 0 0 8px 0;
                    font-size: 15px;
                }}
                .activity-chart {{ 
                    text-align: center; 
                    padding: 15px; 
                    background: #f8f9fa; 
                    border-radius: 8px; 
                }}
                ul {{ 
                    padding-left: 20px; 
                    margin: 10px 0;
                }}
                li {{ 
                    margin: 6px 0; 
                }}
                .footer {{ 
                    text-align: center; 
                    padding: 15px; 
                    background: #f8f9fa; 
                    color: #666; 
                    border-top: 1px solid #e9ecef; 
                    font-size: 12px;
                }}
                p {{
                    margin: 8px 0;
                    font-size: 13px;
                }}
                @media print {{
                    body {{
                        background: white;
                    }}
                    .container {{
                        box-shadow: none;
                    }}
                }}
            </style>
        </head>
        <body>
            <div class="container">
                <div class="header">
                    <h1>📊 Resumo de Desempenho</h1>
                    <p><strong>{performance['name']}</strong></p>
                    <p>Período: {performance['period']}</p>
                </div>
                
                <div class="exam-countdown">
                    🎓 FALTAM {performance['days_to_exam']} DIAS PARA A PROVA (31/08/2025 às 9h)
                </div>
                
                <div class="content">
                    <div class="section">
                        <h2>📈 Métricas Gerais</h2>
                        <div class="metric-row">
                            <div class="metric-card">
                                <div class="metric-number" style="color: #667eea;">{performance['total_questions']}</div>
                                <div class="metric-label">Questões</div>
                            </div>
                            <div class="metric-card">
                                <div class="metric-number" style="color: #4CAF50;">{performance['correct_answers']}</div>
                                <div class="metric-label">Acertos</div>
                            </div>
                            <div class="metric-card">
                                <div class="metric-number" style="color: #F44336;">{performance['incorrect_answers']}</div>
                                <div class="metric-label">Erros</div>
                            </div>
                            <div class="metric-card">
                                <div class="metric-number" style="color: #FF9800;">{performance['accuracy_rate']:.1f}%</div>
                                <div class="metric-label">Aproveitamento</div>
                            </div>
                        </div>
                        
                        <div class="type-metrics">
                            <div class="type-card">
                                <h3>📝 Rapidinhas</h3>
                                <div class="metric-number" style="color: #667eea;">
                                    {performance['rapidinhas']['correct']}/{performance['rapidinhas']['total']}
                                </div>
                                <div class="metric-label">{performance['rapidinhas']['accuracy']:.1f}% de acertos</div>
                            </div>
                            <div class="type-card">
                                <h3>🏃 Sprint / Simulados</h3>
                                <div class="metric-number" style="color: #764ba2;">
                                    {performance['sprint']['correct']}/{performance['sprint']['total']}
                                </div>
                                <div class="metric-label">{performance['sprint']['accuracy']:.1f}% de acertos</div>
                            </div>
                        </div>
                        
                        <h3>📈 Progresso Geral no Banco de Questões</h3>
                        <p>{performance['user_total_answered']} de {performance['total_questions_db']} questões respondidas</p>
                        <div class="progress-bar">
                            <div class="progress-fill" style="width: {progress_percentage:.1f}%;"></div>
                        </div>
                        <p style="text-align: center; color: #666; font-size: 12px;">{progress_percentage:.1f}% do banco explorado</p>
                    </div>
                    
                    <div class="section">
                        <h2>📅 Atividade da Semana</h2>
                        <div class="activity-chart">
                            {activity_bars}
                        </div>
                    </div>
                    
                    <div class="section">
                        <h2>🎯 Performance por Categoria</h2>
                        <div style="margin-bottom: 15px; padding: 10px; background: #f0f0f0; border-radius: 5px; font-size: 12px;">
                            <strong>Legenda:</strong><br>
                            <span style="background: #FF6B6B; color: white; padding: 2px 8px; border-radius: 3px; margin-right: 15px; display: inline-block; margin-top: 5px;">ALTA INCIDÊNCIA</span> Tópicos com alta probabilidade de cair na prova<br>
                            <span style="background: #FFA500; color: white; padding: 2px 8px; border-radius: 3px; margin-right: 15px; display: inline-block; margin-top: 5px;">INCIDÊNCIA MODERADA</span> Tópicos com probabilidade moderada de cair na prova<br>
                            <span style="background: #4ECDC4; color: white; padding: 2px 8px; border-radius: 3px; margin-right: 15px; display: inline-block; margin-top: 5px;">BAIXA INCIDÊNCIA</span> Tópicos com baixa probabilidade de cair na prova
                        </div>
                        {categories_html}
                    </div>
                    
                    <div class="section">
                        <h2>💡 Sugestões Personalizadas</h2>
                        <ul>
                            {suggestions_html}
                        </ul>
                    </div>
                </div>
                
                <div class="footer">
                    <p>Relatório gerado em {datetime.now().strftime('%d/%m/%Y às %H:%M')}</p>
                </div>
            </div>
        </body>
        </html>
        """
        
        return html_template

    def send_user_email(self, user_email, user_name, html_filename, pdf_filename, days_to_exam):
        """Envia e-mail para o usuário com o relatório"""

        if not user_email:
            print(f"   ⚠️ E-mail não encontrado para {user_name}")
            return False

        # Criar assunto personalizado com dias para a prova
        if days_to_exam > 0:
            subject = f"📊 Seu Relatório Semanal - Faltam {days_to_exam} dias para a prova!"
        elif days_to_exam == 0:
            subject = "📊 Seu Relatório Semanal - A prova é HOJE! Boa sorte!"
        else:
            subject = "📊 Seu Relatório Semanal de Desempenho"

        # Verificar se os arquivos existem
        if not os.path.exists(html_filename):
            print(f"   ❌ Arquivo HTML não encontrado: {html_filename}")
            return False

        attachments = []
        if pdf_filename and os.path.exists(pdf_filename):
            attachments.append(pdf_filename)

        try:
            print(f"   📧 Enviando e-mail para: {user_email}")
            success = send_email(
                to=user_email,
                subject=subject,
                body_path=html_filename,
                attachments=attachments if attachments else None,
                is_body_html=True
            )

            if success:
                print(f"   ✅ E-mail enviado com sucesso para {user_name}")
                return True
            else:
                print(f"   ❌ Falha ao enviar e-mail para {user_name}")
                return False

        except Exception as e:
            print(f"   ❌ Erro ao enviar e-mail para {user_name}: {e}")
            return False

def main():
    """Função principal"""

    print("="*60)
    print("        GERADOR DE RESUMO INDIVIDUAL DE USUÁRIO")
    print("           COM ENVIO AUTOMÁTICO DE E-MAILS")
    print("="*60)

    generator = UserSummaryGenerator()

    # Configura logging
    logging.basicConfig(level=logging.INFO, format='%(levelname)s: %(message)s')

    try:
        # Lista usuários disponíveis
        print("\n🔍 Buscando usuários ativos...")
        users_df = generator.get_available_users(days=30)

        if users_df.empty:
            print("❌ Nenhum usuário encontrado com atividade nos últimos 30 dias.")
            return

        print(f"\n👥 {len(users_df)} usuários encontrados:")
        print("-" * 80)
        print(f"{'ID':<6} {'Nome':<30} {'Questões':<10} {'Última Atividade':<15}")
        print("-" * 80)

        for _, user in users_df.iterrows():
            name = user['ds_whatname'] or user['ds_whatinternalname'] or f"Usuário {user['id_user']}"
            last_activity = pd.to_datetime(user['ultima_atividade']).strftime('%d/%m/%Y')
            print(f"{user['id_user']:<6} {name[:29]:<30} {user['total_questoes']:<10} {last_activity:<15}")

        print("-" * 80)

        # Calcular automaticamente o período da última semana completa (segunda a domingo)
        today = datetime.now()
        days_since_sunday = (today.weekday() + 1) % 7
        last_sunday = today - timedelta(days=days_since_sunday)
        last_monday = last_sunday - timedelta(days=6)
        if today.weekday() == 6:  # Domingo
            last_sunday = last_sunday - timedelta(days=7)
            last_monday = last_monday - timedelta(days=7)
        days = (today - last_monday).days + 1

        print(f"\n📊 Gerando relatórios da última semana completa para todos os usuários...")
        print(f"   Período: {last_monday.strftime('%d/%m/%Y')} (segunda) a {last_sunday.strftime('%d/%m/%Y')} (domingo)")
        print(f"📧 Os relatórios serão enviados automaticamente por e-mail para cada usuário")

        for _, user in users_df.iterrows():
            user_id = user['id_user']
            user_name = user['ds_whatname'] or user['ds_whatinternalname'] or f"Usuário {user_id}"
            user_email = user['ds_mail']
            print(f"\n➡️  Gerando para: {user_name} (ID: {user_id}) - E-mail: {user_email}")

            # Busca dados do usuário
            user_data = generator.get_user_data(user_id, days)
            user_data['dt_answer'] = pd.to_datetime(user_data['dt_answer'])
            user_data = user_data[
                (user_data['dt_answer'].dt.date >= last_monday.date()) &
                (user_data['dt_answer'].dt.date <= last_sunday.date())
            ]

            if user_data.empty:
                print(f"   ⚠️ Nenhuma atividade encontrada na semana. Gerando relatório zerado.")
                performance = {
                    'user_id': user_id,
                    'name': user_name,
                    'total_questions': 0,
                    'correct_answers': 0,
                    'incorrect_answers': 0,
                    'accuracy_rate': 0.0,
                    'rapidinhas': {'total': 0, 'correct': 0, 'accuracy': 0.0},
                    'sprint': {'total': 0, 'correct': 0, 'accuracy': 0.0},
                    'categories_performance': {},
                    'difficulty_performance': {},
                    'activity_by_day': {},
                    'period': f"{last_monday.strftime('%d/%m/%Y')} a {last_sunday.strftime('%d/%m/%Y')}",
                    'unique_days_studied': 0,
                    'period_days': (last_sunday - last_monday).days + 1,
                    'days_without_activity': (last_sunday - last_monday).days + 1,
                    'user_total_answered': 0,
                    'total_questions_db': generator.get_total_questions_in_database(),
                    'days_to_exam': (EXAM_DATE - datetime.now()).days
                }
            else:
                performance = generator.analyze_user_performance(user_data, 7)
                performance['period'] = f"{last_monday.strftime('%d/%m/%Y')} a {last_sunday.strftime('%d/%m/%Y')}"

            # Gerar HTML e PDF
            temp_html = f"resumo_usuario_{user_id}_semana_{last_monday.strftime('%Y%m%d')}_{last_sunday.strftime('%Y%m%d')}.html"
            html_content = generator.generate_html_summary(performance)
            pdf_filename = None

            try:
                with open(temp_html, 'w', encoding='utf-8') as f:
                    f.write(html_content)
                pdf_filename = temp_html.replace('.html', '.pdf')
                pdf_result = generator.save_summary_to_pdf(temp_html, pdf_filename)
                if pdf_result:
                    print(f"   ✅ PDF gerado: {pdf_filename}")
                else:
                    print(f"   ⚠️ PDF não foi gerado automaticamente. HTML salvo como: {temp_html}")
                    pdf_filename = None  # Se não conseguiu gerar PDF, não anexar

                # Enviar e-mail para o usuário
                generator.send_user_email(
                    user_email=user_email,
                    user_name=user_name,
                    html_filename=temp_html,
                    pdf_filename=pdf_filename,
                    days_to_exam=performance['days_to_exam']
                )

            except Exception as e:
                print(f"   ❌ Erro ao gerar arquivos: {e}")

        print("\n✅ Relatórios gerados e e-mails enviados para todos os usuários.")

    except KeyboardInterrupt:
        print("\n\n👋 Operação cancelada pelo usuário.")
    except Exception as e:
        print(f"\n❌ Erro inesperado: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()