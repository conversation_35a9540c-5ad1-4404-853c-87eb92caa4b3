import os
import base64
from email.message import EmailMessage
from google.oauth2.credentials import Credentials
from google.auth.exceptions import RefreshError
from googleapiclient.discovery import build
from googleapiclient.errors import HttpError

CRED = 'SysDev/Python/email_sender/credentials.json' SysDev/Python/email_sender/credentials.json
TOK = 'SysDev/Python/email_sender/token.json'

def send_email(
    to,
    subject,
    body_path=None,
    from_addr="<EMAIL>",
    attachments=None,
    is_body_html=False
):
    # Verificar arquivos de autenticação
    credentials_path = CRED 
    if not os.path.exists(credentials_path):
        credentials_path = CRED
        if not os.path.exists(credentials_path):
            print("❌ Arquivo 'credentials.json' não encontrado.")
            return False

    if not os.path.exists(TOK):
        print("❌ Arquivo 'token.json' não encontrado. Execute o fluxo de autorização primeiro.")
        return False

    # Carregar credenciais
    try:
        creds = Credentials.from_authorized_user_file(TOK, ["https://www.googleapis.com/auth/gmail.send"])
    except RefreshError as e:
        print(f"❌ Erro ao carregar token: {e}")
        return False

    # Criar mensagem
    message = EmailMessage()
    message["To"] = to
    message["From"] = from_addr
    message["Subject"] = subject

    # Corpo do e-mail
    if body_path:
        if not os.path.exists(body_path):
            print(f"❌ Arquivo de corpo '{body_path}' não encontrado.")
            return False
        with open(body_path, "r", encoding="utf-8") as f:
            body_content = f.read()
        if is_body_html:
            message.add_alternative(body_content, subtype="html")
        else:
            message.set_content(body_content)
    else:
        message.set_content("")

    # Anexos
    if attachments:
        if isinstance(attachments, str):
            attachments = [attachments]
        for attach_path in attachments:
            if not os.path.exists(attach_path):
                print(f"❌ Anexo '{attach_path}' não encontrado.")
                return False
            with open(attach_path, "rb") as f:
                data = f.read()
                maintype, subtype = "application", "octet-stream"
                if attach_path.lower().endswith(".pdf"):
                    subtype = "pdf"
                filename = os.path.basename(attach_path)
                message.add_attachment(data, maintype=maintype, subtype=subtype, filename=filename)

    # Codificar e enviar
    try:
        encoded_message = base64.urlsafe_b64encode(message.as_bytes()).decode()
        service = build("gmail", "v1", credentials=creds)
        send_message = {"raw": encoded_message}
        response = service.users().messages().send(userId="me", body=send_message).execute()
        print(f"✅ Mensagem enviada! ID: {response['id']}")
        return True
    except HttpError as error:
        print(f"❌ Erro ao enviar: {error}")
        return False

# Exemplo de uso:
send_email(
    to="<EMAIL>",
    subject="Teste",
    body_path="Teste",
    from_addr="<EMAIL>",
    attachments=["anexo.pdf"],
    is_body_html=True
)