from google_auth_oauthlib.flow import InstalledAppFlow
from google.auth.transport.requests import Request
from google.oauth2.credentials import Credentials
import os.path
import pickle

SCOPES = ['https://www.googleapis.com/auth/gmail.send']

def main():
    creds = None
    if os.path.exists('SysDev/Python/email_sender/credentials.json'):
        with open('SysDev/Python/email_sender/credentials.json', 'r') as token:
            creds = Credentials.from_authorized_user_file('SysDev/Python/email_sender/credentials.json', SCOPES)
    if not creds or not creds.valid:
        if creds and creds.expired and creds.refresh_token:
            creds.refresh(Request())
        else:
            flow = InstalledAppFlow.from_client_secrets_file('SysDev/Python/email_sender/credentials.json', SCOPES)
            creds = flow.run_local_server(port=0)
        with open('SysDev/Python/email_sender/token.json', 'w') as token:
            token.write(creds.to_json())

main()